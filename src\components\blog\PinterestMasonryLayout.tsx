"use client";

import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { PinterestBlogCard } from "./PinterestBlogCard";

interface BlogPost {
  id?: string;
  _id?: string;
  title: string;
  excerpt?: string;
  description?: string;
  content?: string;
  slug: string;
  featuredImage?: string;
  image?: string;
  imageCredit?: string;
  categoryId?: string;
  category?: string;
  categories?: string[];
  tags?: string[];
  publishedAt?: string;
  createdAt?: string;
  date?: string;
  author: {
    name: string;
    email?: string;
  } | string;
}

interface PinterestMasonryLayoutProps {
  posts: BlogPost[];
  loading?: boolean;
  showAnimation?: boolean;
  className?: string;
}

export function PinterestMasonryLayout({ 
  posts, 
  loading = false, 
  showAnimation = true,
  className = ""
}: PinterestMasonryLayoutProps) {
  const [columns, setColumns] = useState(3);
  const [mounted, setMounted] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle mounting to prevent hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  // Responsive column calculation
  useEffect(() => {
    const updateColumns = () => {
      const width = window.innerWidth;
      if (width < 640) {
        setColumns(1); // Mobile: 1 column
      } else if (width < 1024) {
        setColumns(2); // Tablet: 2 columns
      } else if (width < 1536) {
        setColumns(3); // Desktop: 3 columns
      } else {
        setColumns(4); // Large desktop: 4 columns
      }
    };

    if (mounted) {
      updateColumns();
      window.addEventListener('resize', updateColumns);
      return () => window.removeEventListener('resize', updateColumns);
    }
  }, [mounted]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const columnVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  // Loading skeleton
  if (loading || !mounted) {
    return (
      <div className={`${className}`}>
        <div 
          className="gap-6"
          style={{
            columns: `${columns}`,
            columnGap: '1.5rem'
          }}
        >
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="break-inside-avoid mb-6 animate-pulse">
              <div className="bg-muted rounded-3xl overflow-hidden">
                <div className="h-64 bg-muted/80"></div>
                <div className="p-6 space-y-3">
                  <div className="h-3 bg-muted/80 rounded w-1/4"></div>
                  <div className="h-5 bg-muted/80 rounded w-3/4"></div>
                  <div className="h-4 bg-muted/80 rounded w-full"></div>
                  <div className="h-4 bg-muted/80 rounded w-2/3"></div>
                  <div className="flex justify-between items-center pt-4">
                    <div className="h-4 bg-muted/80 rounded w-1/3"></div>
                    <div className="h-4 bg-muted/80 rounded w-1/4"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Empty state
  if (!posts.length) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-20"
      >
        <div className="max-w-md mx-auto">
          <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-muted flex items-center justify-center">
            <svg className="w-12 h-12 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2 text-foreground">No articles found</h3>
          <p className="text-muted-foreground">
            Check back later for new content or try adjusting your search filters.
          </p>
        </div>
      </motion.div>
    );
  }

  return (
    <>
      {/* Pinterest-specific styles */}
      <style jsx>{`
        .pinterest-masonry {
          column-fill: auto;
        }
        .pinterest-masonry > * {
          display: inline-block;
          width: 100%;
          margin-bottom: 1rem;
          break-inside: avoid;
          page-break-inside: avoid;
        }
        @media (max-width: 640px) {
          .pinterest-masonry {
            column-count: 1 !important;
          }
        }
        @media (min-width: 641px) and (max-width: 1023px) {
          .pinterest-masonry {
            column-count: 2 !important;
          }
        }
        @media (min-width: 1024px) and (max-width: 1279px) {
          .pinterest-masonry {
            column-count: 3 !important;
          }
        }
        @media (min-width: 1280px) and (max-width: 1535px) {
          .pinterest-masonry {
            column-count: 4 !important;
          }
        }
        @media (min-width: 1536px) {
          .pinterest-masonry {
            column-count: 5 !important;
          }
        }
      `}</style>

      <motion.div
        ref={containerRef}
        variants={showAnimation ? containerVariants : undefined}
        initial={showAnimation ? "hidden" : false}
        animate={showAnimation ? "visible" : false}
        className={`${className}`}
      >
      {/* Enhanced CSS Columns Masonry Layout for Pinterest-like behavior */}
      <div
        className="pinterest-masonry"
        style={{
          columns: `${columns}`,
          columnGap: '1rem', // Tighter gap for Pinterest feel
          columnFill: 'auto', // Changed from 'balance' for more natural flow
          orphans: 1,
          widows: 1
        }}
      >
        {posts.map((post, index) => (
          <motion.div
            key={post.id || post._id || index}
            variants={showAnimation ? {
              hidden: { opacity: 0, y: 30, scale: 0.95 },
              visible: {
                opacity: 1,
                y: 0,
                scale: 1,
                transition: {
                  duration: 0.6,
                  delay: index * 0.1,
                  ease: [0.25, 0.46, 0.45, 0.94]
                }
              }
            } : undefined}
            className="break-inside-avoid mb-4 inline-block w-full"
          >
            <PinterestBlogCard
              post={post}
              index={index}
              showAnimation={false} // We handle animation here
              className="w-full"
            />
          </motion.div>
        ))}
      </div>
    </motion.div>
    </>
  );
}

// Alternative Grid-based Masonry (fallback for better browser support)
export function PinterestGridLayout({ 
  posts, 
  loading = false, 
  showAnimation = true,
  className = ""
}: PinterestMasonryLayoutProps) {
  const [columns, setColumns] = useState(3);
  const [columnPosts, setColumnPosts] = useState<BlogPost[][]>([]);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Enhanced responsive column calculation for Pinterest-like behavior
  useEffect(() => {
    const updateColumns = () => {
      const width = window.innerWidth;
      // More Pinterest-like breakpoints with better spacing
      if (width < 640) {
        setColumns(1);
      } else if (width < 768) {
        setColumns(2);
      } else if (width < 1024) {
        setColumns(2);
      } else if (width < 1280) {
        setColumns(3);
      } else if (width < 1536) {
        setColumns(4);
      } else {
        setColumns(5); // More columns for very large screens
      }
    };

    if (mounted) {
      updateColumns();
      window.addEventListener('resize', updateColumns);
      return () => window.removeEventListener('resize', updateColumns);
    }
  }, [mounted]);

  // Distribute posts across columns for balanced layout
  useEffect(() => {
    if (!posts.length || !mounted) {
      setColumnPosts([]);
      return;
    }

    const newColumnPosts: BlogPost[][] = Array.from({ length: columns }, () => []);
    
    // Simple round-robin distribution
    posts.forEach((post, index) => {
      const columnIndex = index % columns;
      newColumnPosts[columnIndex].push(post);
    });

    setColumnPosts(newColumnPosts);
  }, [posts, columns, mounted]);

  if (loading || !mounted) {
    return (
      <div className={`grid gap-6 ${className}`} style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, colIndex) => (
          <div key={colIndex} className="flex flex-col gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-muted rounded-3xl overflow-hidden">
                  <div className="h-64 bg-muted/80"></div>
                  <div className="p-6 space-y-3">
                    <div className="h-3 bg-muted/80 rounded w-1/4"></div>
                    <div className="h-5 bg-muted/80 rounded w-3/4"></div>
                    <div className="h-4 bg-muted/80 rounded w-full"></div>
                    <div className="h-4 bg-muted/80 rounded w-2/3"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  }

  if (!posts.length) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-20"
      >
        <div className="max-w-md mx-auto">
          <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-muted flex items-center justify-center">
            <svg className="w-12 h-12 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2 text-foreground">No articles found</h3>
          <p className="text-muted-foreground">
            Check back later for new content or try adjusting your search filters.
          </p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      variants={showAnimation ? {
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            staggerChildren: 0.1,
            delayChildren: 0.2
          }
        }
      } : undefined}
      initial={showAnimation ? "hidden" : false}
      animate={showAnimation ? "visible" : false}
      className={`grid gap-6 ${className}`}
      style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
    >
      {columnPosts.map((columnPosts, columnIndex) => (
        <motion.div
          key={columnIndex}
          variants={showAnimation ? {
            hidden: { opacity: 0, y: 20 },
            visible: {
              opacity: 1,
              y: 0,
              transition: {
                staggerChildren: 0.05
              }
            }
          } : undefined}
          className="flex flex-col gap-6"
        >
          {columnPosts.map((post, postIndex) => (
            <motion.div
              key={post.id || post._id || postIndex}
              variants={showAnimation ? {
                hidden: { opacity: 0, y: 20, scale: 0.95 },
                visible: {
                  opacity: 1,
                  y: 0,
                  scale: 1,
                  transition: {
                    duration: 0.5,
                    delay: (columnIndex * 0.1) + (postIndex * 0.05),
                    ease: "easeOut"
                  }
                }
              } : undefined}
            >
              <PinterestBlogCard
                post={post}
                index={columnIndex * columnPosts.length + postIndex}
                showAnimation={false}
                className="w-full"
              />
            </motion.div>
          ))}
        </motion.div>
      ))}
    </motion.div>
  );
}
